/**
 * 桂林智源 SVG 数字化系统 - MQTT 客户端工具类
 * 适用于浏览器环境的 MQTT 连接和数据处理工具
 *
 * 功能特性：
 * - 自动连接和重连机制
 * - 数据订阅和发布
 * - 错误处理和日志记录
 * - 电气系统实时数据更新
 */

// 全局 MQTT 工具对象
let mqttTool = {
    client: null,
    isConnected: false,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    reconnectInterval: 5000,
    subscriptions: new Set(),
    messageHandlers: new Map(),

    // 配置参数
    config: {
        brokerUrl: '',
        username: 'FastBee',
        password: '',
        clientId: 'svg-web-' + Math.random().toString(16).substr(2, 8),
        keepAlive: 30,
        connectTimeout: 60000,
        cleanSession: true,
        // 电气系统订阅主题
        electricalTopic: '/189/D19QBHKRZ791U/function/get'
    }
};

/**
 * 初始化 MQTT 连接配置
 */
mqttTool.init = function() {
    console.log('[MQTT] 初始化 MQTT 工具...');

    // 获取认证信息
    const search = new URLSearchParams(window.location.search);
    const share = search.get('share');
    this.config.password = share || 'default_token';

    // 配置 MQTT 服务器地址
    if (!this.config.brokerUrl) {
        console.log('[MQTT] 自动获取 MQTT 连接地址');
        if (window.location.protocol === 'http:') {
            this.config.brokerUrl = 'ws://' + window.location.hostname + ':8083/mqtt';
        } else {
            this.config.brokerUrl = 'wss://' + window.location.hostname + '/mqtt';
        }
    }

    console.log('[MQTT] 配置完成:', {
        brokerUrl: this.config.brokerUrl,
        clientId: this.config.clientId,
        electricalTopic: this.config.electricalTopic
    });
};

/**
 * 连接到 MQTT 服务器
 */
mqttTool.connect = function () {
    if (this.client && this.isConnected) {
        console.log('[MQTT] 已经连接，跳过重复连接');
        return Promise.resolve('already_connected');
    }

    return new Promise((resolve, reject) => {
        try {
            console.log('[MQTT] 开始连接到服务器:', this.config.brokerUrl);

            // 连接选项
            const options = {
                username: this.config.username,
                password: this.config.password,
                clientId: this.config.clientId,
                keepalive: this.config.keepAlive,
                connectTimeout: this.config.connectTimeout,
                clean: this.config.cleanSession,
                reconnectPeriod: 0, // 禁用自动重连，使用自定义重连逻辑
            };

            // 创建连接（需要在页面中引入 MQTT.js 库）
            if (typeof mqtt === 'undefined') {
                throw new Error('MQTT 库未加载，请确保已引入 mqtt.min.js');
            }

            this.client = mqtt.connect(this.config.brokerUrl, options);

            // 连接成功事件
            this.client.on('connect', () => {
                console.log('[MQTT] 连接成功');
                this.isConnected = true;
                this.reconnectAttempts = 0;

                // 自动订阅电气系统主题
                this.subscribeElectricalData();

                resolve('connected');
            });

            // 消息接收事件
            this.client.on('message', (topic, message) => {
                this.handleMessage(topic, message);
            });

            // 重新连接事件
            this.client.on('reconnect', () => {
                console.log('[MQTT] 正在重连...');
            });

            // 错误事件
            this.client.on('error', (error) => {
                console.error('[MQTT] 连接错误:', error);
                this.isConnected = false;

                // 尝试重连
                this.handleReconnect();

                reject(error);
            });

            // 断开连接事件
            this.client.on('close', () => {
                console.log('[MQTT] 连接已断开');
                this.isConnected = false;

                // 尝试重连
                this.handleReconnect();
            });

            // 离线事件
            this.client.on('offline', () => {
                console.log('[MQTT] 客户端离线');
                this.isConnected = false;
            });

        } catch (error) {
            console.error('[MQTT] 连接初始化失败:', error);
            reject(error);
        }
    });
};
/**
 * 处理重连逻辑
 */
mqttTool.handleReconnect = function() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.error('[MQTT] 达到最大重连次数，停止重连');
        return;
    }

    this.reconnectAttempts++;
    console.log(`[MQTT] 第 ${this.reconnectAttempts} 次重连尝试...`);

    setTimeout(() => {
        this.connect().catch(error => {
            console.error('[MQTT] 重连失败:', error);
        });
    }, this.reconnectInterval);
};

/**
 * 订阅电气系统数据主题
 */
mqttTool.subscribeElectricalData = function() {
    const topic = this.config.electricalTopic;
    console.log('[MQTT] 订阅电气系统数据主题:', topic);

    this.subscribe(topic).then(result => {
        console.log('[MQTT] 电气系统数据订阅结果:', result);

        // 注册消息处理器
        this.registerMessageHandler(topic, this.handleElectricalData.bind(this));
    }).catch(error => {
        console.error('[MQTT] 电气系统数据订阅失败:', error);
    });
};

/**
 * 处理接收到的消息
 */
mqttTool.handleMessage = function(topic, message) {
    try {
        console.log('[MQTT] 收到消息:', { topic, message: message.toString() });

        // 查找对应的消息处理器
        const handler = this.messageHandlers.get(topic);
        if (handler) {
            handler(topic, message);
        } else {
            console.warn('[MQTT] 未找到主题处理器:', topic);
        }
    } catch (error) {
        console.error('[MQTT] 消息处理错误:', error);
    }
};

/**
 * 注册消息处理器
 */
mqttTool.registerMessageHandler = function(topic, handler) {
    this.messageHandlers.set(topic, handler);
    console.log('[MQTT] 注册消息处理器:', topic);
};

/**
 * 处理电气系统数据
 */
mqttTool.handleElectricalData = function(topic, message) {
    try {
        const data = JSON.parse(message.toString());
        console.log('[MQTT] 解析电气系统数据:', data);

        // 更新界面显示
        if (typeof updateElectricalParametersFromMQTT === 'function') {
            updateElectricalParametersFromMQTT(data);
        } else {
            console.warn('[MQTT] 界面更新函数未定义');
        }

        // 记录数据接收日志
        if (typeof addAlarmLog === 'function') {
            addAlarmLog('recovery', 'MQTT数据', '接收到电气系统实时数据');
        }

    } catch (error) {
        console.error('[MQTT] 电气系统数据解析失败:', error);

        // 记录错误日志
        if (typeof addAlarmLog === 'function') {
            addAlarmLog('fault', 'MQTT数据', '数据解析失败: ' + error.message);
        }
    }
};

/**
 * 断开连接
 */
mqttTool.end = function () {
    return new Promise((resolve) => {
        if (!this.client) {
            console.log('[MQTT] 未连接');
            resolve('未连接');
            return;
        }

        console.log('[MQTT] 断开连接...');
        this.client.end();
        this.client = null;
        this.isConnected = false;
        this.subscriptions.clear();
        this.messageHandlers.clear();

        console.log('[MQTT] 连接已断开');
        resolve('连接终止');
    });
};

/**
 * 手动重连
 */
mqttTool.reconnect = function () {
    return new Promise((resolve) => {
        if (!this.client) {
            console.log('[MQTT] 未连接，开始新连接');
            this.connect().then(resolve).catch(resolve);
            return;
        }

        console.log('[MQTT] 手动重连...');
        this.client.reconnect();
        resolve('重连中');
    });
};

/**
 * 消息订阅
 */
mqttTool.subscribe = function (topics) {
    return new Promise((resolve, reject) => {
        if (!this.client || !this.isConnected) {
            const error = 'MQTT未连接';
            console.error('[MQTT]', error);
            reject(new Error(error));
            return;
        }

        // 确保 topics 是数组格式
        const topicArray = Array.isArray(topics) ? topics : [topics];

        this.client.subscribe(topicArray, { qos: 1 }, (err, granted) => {
            if (err) {
                console.error('[MQTT] 订阅失败:', err);
                reject(err);
                return;
            }

            console.log('[MQTT] 订阅成功:', granted);

            // 记录订阅的主题
            topicArray.forEach(topic => {
                this.subscriptions.add(topic);
            });

            resolve('订阅成功');
        });
    });
};
/**
 * 取消订阅
 */
mqttTool.unsubscribe = function (topics) {
    return new Promise((resolve, reject) => {
        if (!this.client || !this.isConnected) {
            const error = 'MQTT未连接';
            console.error('[MQTT]', error);
            reject(new Error(error));
            return;
        }

        // 确保 topics 是数组格式
        const topicArray = Array.isArray(topics) ? topics : [topics];

        this.client.unsubscribe(topicArray, (err) => {
            if (err) {
                console.error('[MQTT] 取消订阅失败:', err);
                reject(err);
                return;
            }

            console.log('[MQTT] 取消订阅成功:', topicArray);

            // 从订阅记录中移除
            topicArray.forEach(topic => {
                this.subscriptions.delete(topic);
                this.messageHandlers.delete(topic);
            });

            resolve('取消订阅成功');
        });
    });
};

/**
 * 发布消息
 */
mqttTool.publish = function (topic, message, name = '系统') {
    return new Promise((resolve, reject) => {
        if (!this.client || !this.isConnected) {
            const error = 'MQTT客户端未连接';
            console.error('[MQTT]', error);
            reject(new Error(error));
            return;
        }

        const messageStr = typeof message === 'string' ? message : JSON.stringify(message);

        this.client.publish(topic, messageStr, { qos: 1 }, (err) => {
            console.log('[MQTT] 发送主题:', topic);
            console.log('[MQTT] 发送内容:', messageStr);

            if (err) {
                const errorMsg = `[${name}] 指令发送失败`;
                console.error('[MQTT]', errorMsg, err);
                reject(new Error(errorMsg));
                return;
            }

            const successMsg = topic.indexOf('offline') > 0
                ? `[${name}] 影子指令发送成功`
                : `[${name}] 指令发送成功`;

            console.log('[MQTT]', successMsg);
            resolve(successMsg);
        });
    });
};

/**
 * 获取连接状态
 */
mqttTool.getStatus = function() {
    return {
        isConnected: this.isConnected,
        reconnectAttempts: this.reconnectAttempts,
        subscriptions: Array.from(this.subscriptions),
        clientId: this.config.clientId,
        brokerUrl: this.config.brokerUrl
    };
};

/**
 * 启动 MQTT 服务
 * 这是主要的入口函数，用于初始化和启动 MQTT 连接
 */
mqttTool.start = function() {
    console.log('[MQTT] 启动 MQTT 服务...');

    // 初始化配置
    this.init();

    // 开始连接
    return this.connect().then(() => {
        console.log('[MQTT] MQTT 服务启动成功');
        return 'started';
    }).catch(error => {
        console.error('[MQTT] MQTT 服务启动失败:', error);
        throw error;
    });
};

// 导出 MQTT 工具对象（浏览器环境）
if (typeof window !== 'undefined') {
    window.mqttTool = mqttTool;
}

// 兼容 CommonJS 和 ES6 模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = mqttTool;
}

// 默认导出
if (typeof exports !== 'undefined') {
    exports.default = mqttTool;
}
