/**
 * 桂林智源 SVG 数字化系统 - MQTT 数据解析器
 * 用于解析从 MQTT 接收到的电气系统数据，并映射到界面显示元素
 * 
 * 功能特性：
 * - 基于物模型配置解析数据
 * - 数据验证和格式化
 * - 参数映射和单位转换
 * - 错误处理和日志记录
 */

// 全局数据解析器对象
const mqttDataParser = {
    // 物模型配置缓存
    deviceModel: null,
    
    // 参数映射表：物模型ID -> 界面元素ID
    parameterMapping: {
        'HMI_32030': 'bus-voltage-uab-value',      // 母线电压Uab
        'HMI_32050': 'power-factor-value',         // 功率因数
        'HMI_32031': 'bus-voltage-ubc-value',      // 母线电压Ubc (假设)
        'HMI_32032': 'bus-voltage-uca-value',      // 母线电压Uca (假设)
        'HMI_32040': 'svg-current-ia-value',       // SVG电流Ia (假设)
        'HMI_32041': 'svg-current-ib-value',       // SVG电流Ib (假设)
        'HMI_32042': 'svg-current-ic-value',       // SVG电流Ic (假设)
        'HMI_32060': 'load-reactive-power-value',  // 负载无功功率 (假设)
        'HMI_32070': 'grid-reactive-current-value' // 网侧负载无功电流 (假设)
    },
    
    // 单位映射表
    unitMapping: {
        'HMI_32030': 'kV',      // 母线电压
        'HMI_32031': 'kV',      // 母线电压
        'HMI_32032': 'kV',      // 母线电压
        'HMI_32040': 'A',       // 电流
        'HMI_32041': 'A',       // 电流
        'HMI_32042': 'A',       // 电流
        'HMI_32050': '',        // 功率因数（无单位）
        'HMI_32060': 'MVAr',    // 无功功率
        'HMI_32070': 'A'        // 电流
    },
    
    // 数据范围验证配置
    validationRules: {
        'HMI_32030': { min: 8.0, max: 12.0 },      // 母线电压范围
        'HMI_32031': { min: 8.0, max: 12.0 },      // 母线电压范围
        'HMI_32032': { min: 8.0, max: 12.0 },      // 母线电压范围
        'HMI_32040': { min: 0, max: 200 },         // 电流范围
        'HMI_32041': { min: 0, max: 200 },         // 电流范围
        'HMI_32042': { min: 0, max: 200 },         // 电流范围
        'HMI_32050': { min: 0.8, max: 1.0 },       // 功率因数范围
        'HMI_32060': { min: -5.0, max: 5.0 },      // 无功功率范围
        'HMI_32070': { min: 0, max: 300 }          // 电流范围
    }
};

/**
 * 初始化数据解析器
 */
mqttDataParser.init = function() {
    console.log('[DataParser] 初始化数据解析器...');
    
    // 加载物模型配置
    this.loadDeviceModel().then(() => {
        console.log('[DataParser] 数据解析器初始化完成');
    }).catch(error => {
        console.error('[DataParser] 数据解析器初始化失败:', error);
    });
};

/**
 * 加载设备物模型配置
 */
mqttDataParser.loadDeviceModel = function() {
    return new Promise((resolve, reject) => {
        try {
            // 使用 fetch API 加载物模型文件
            fetch('./物模型/电气拓扑.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    this.deviceModel = data;
                    console.log('[DataParser] 物模型加载成功:', data);
                    resolve(data);
                })
                .catch(error => {
                    console.error('[DataParser] 物模型加载失败:', error);
                    reject(error);
                });
        } catch (error) {
            console.error('[DataParser] 物模型加载异常:', error);
            reject(error);
        }
    });
};

/**
 * 解析 MQTT 数据
 * @param {Object} rawData - 原始 MQTT 数据
 * @returns {Object} 解析后的数据对象
 */
mqttDataParser.parseData = function(rawData) {
    try {
        console.log('[DataParser] 开始解析数据:', rawData);
        
        const parsedData = {
            timestamp: new Date(),
            parameters: {},
            errors: [],
            warnings: []
        };
        
        // 检查数据格式
        if (!rawData || typeof rawData !== 'object') {
            throw new Error('无效的数据格式');
        }
        
        // 解析属性数据
        if (rawData.properties) {
            this.parseProperties(rawData.properties, parsedData);
        }
        
        // 解析事件数据
        if (rawData.events) {
            this.parseEvents(rawData.events, parsedData);
        }
        
        // 解析功能数据
        if (rawData.functions) {
            this.parseFunctions(rawData.functions, parsedData);
        }
        
        console.log('[DataParser] 数据解析完成:', parsedData);
        return parsedData;
        
    } catch (error) {
        console.error('[DataParser] 数据解析失败:', error);
        return {
            timestamp: new Date(),
            parameters: {},
            errors: [error.message],
            warnings: []
        };
    }
};

/**
 * 解析属性数据
 * @param {Object} properties - 属性数据
 * @param {Object} parsedData - 解析结果对象
 */
mqttDataParser.parseProperties = function(properties, parsedData) {
    try {
        for (const [propertyId, value] of Object.entries(properties)) {
            // 验证参数ID是否在映射表中
            if (!this.parameterMapping[propertyId]) {
                parsedData.warnings.push(`未知参数ID: ${propertyId}`);
                continue;
            }
            
            // 数据验证
            const validationResult = this.validateValue(propertyId, value);
            if (!validationResult.isValid) {
                parsedData.errors.push(`参数 ${propertyId} 验证失败: ${validationResult.error}`);
                continue;
            }
            
            // 格式化数值
            const formattedValue = this.formatValue(propertyId, value);
            
            // 添加到解析结果
            parsedData.parameters[propertyId] = {
                elementId: this.parameterMapping[propertyId],
                rawValue: value,
                formattedValue: formattedValue,
                unit: this.unitMapping[propertyId] || '',
                displayText: formattedValue + (this.unitMapping[propertyId] ? ' ' + this.unitMapping[propertyId] : '')
            };
        }
    } catch (error) {
        console.error('[DataParser] 属性解析失败:', error);
        parsedData.errors.push('属性解析失败: ' + error.message);
    }
};

/**
 * 解析事件数据
 * @param {Array} events - 事件数据
 * @param {Object} parsedData - 解析结果对象
 */
mqttDataParser.parseEvents = function(events, parsedData) {
    try {
        if (!Array.isArray(events)) {
            return;
        }
        
        parsedData.events = events.map(event => ({
            id: event.id || 'unknown',
            name: event.name || '未知事件',
            timestamp: event.timestamp || new Date().toISOString(),
            level: event.level || 'info',
            message: event.message || ''
        }));
        
        console.log('[DataParser] 解析到事件数据:', parsedData.events);
    } catch (error) {
        console.error('[DataParser] 事件解析失败:', error);
        parsedData.errors.push('事件解析失败: ' + error.message);
    }
};

/**
 * 解析功能数据
 * @param {Object} functions - 功能数据
 * @param {Object} parsedData - 解析结果对象
 */
mqttDataParser.parseFunctions = function(functions, parsedData) {
    try {
        parsedData.functions = functions;
        console.log('[DataParser] 解析到功能数据:', functions);
    } catch (error) {
        console.error('[DataParser] 功能解析失败:', error);
        parsedData.errors.push('功能解析失败: ' + error.message);
    }
};

/**
 * 验证数值
 * @param {string} propertyId - 参数ID
 * @param {*} value - 参数值
 * @returns {Object} 验证结果
 */
mqttDataParser.validateValue = function(propertyId, value) {
    try {
        // 检查是否为数值
        const numValue = parseFloat(value);
        if (isNaN(numValue)) {
            return { isValid: false, error: '不是有效数值' };
        }
        
        // 检查范围
        const rule = this.validationRules[propertyId];
        if (rule) {
            if (numValue < rule.min || numValue > rule.max) {
                return { 
                    isValid: false, 
                    error: `数值超出范围 [${rule.min}, ${rule.max}]` 
                };
            }
        }
        
        return { isValid: true, value: numValue };
    } catch (error) {
        return { isValid: false, error: error.message };
    }
};

/**
 * 格式化数值
 * @param {string} propertyId - 参数ID
 * @param {*} value - 参数值
 * @returns {string} 格式化后的值
 */
mqttDataParser.formatValue = function(propertyId, value) {
    try {
        const numValue = parseFloat(value);
        
        // 根据参数类型确定小数位数
        if (propertyId === 'HMI_32050') {
            // 功率因数保留3位小数
            return numValue.toFixed(3);
        } else if (propertyId.startsWith('HMI_3203')) {
            // 电压保留2位小数
            return numValue.toFixed(2);
        } else if (propertyId.startsWith('HMI_3204') || propertyId === 'HMI_32070') {
            // 电流保留1位小数
            return numValue.toFixed(1);
        } else if (propertyId === 'HMI_32060') {
            // 功率保留2位小数
            return numValue.toFixed(2);
        }
        
        // 默认保留2位小数
        return numValue.toFixed(2);
    } catch (error) {
        console.error('[DataParser] 数值格式化失败:', error);
        return String(value);
    }
};

/**
 * 获取参数映射信息
 * @param {string} propertyId - 参数ID
 * @returns {Object} 映射信息
 */
mqttDataParser.getParameterInfo = function(propertyId) {
    return {
        elementId: this.parameterMapping[propertyId],
        unit: this.unitMapping[propertyId],
        validation: this.validationRules[propertyId]
    };
};

// 导出数据解析器对象（浏览器环境）
if (typeof window !== 'undefined') {
    window.mqttDataParser = mqttDataParser;
}

// 兼容 CommonJS 和 ES6 模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = mqttDataParser;
}
